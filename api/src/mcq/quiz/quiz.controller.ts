import { CLIENT_TYPE } from '@/guards/request-validation.decorator';
import { RoleGuard } from '@/guards/role.guard';
import { AppClients } from '@app/shared/constants/auth.constants';
import {
  BadRequestException,
  Body,
  Controller,
  Delete,
  Get,
  Logger,
  Param,
  Post,
  Put,
  Query,
  UseGuards,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiOkResponse,
  ApiTags,
  ApiOperation,
  ApiCreatedResponse,
  ApiParam,
  ApiQuery,
  ApiBody,
  ApiNotFoundResponse,
  ApiBadRequestResponse,
  ApiUnauthorizedResponse,
} from '@nestjs/swagger';
import { UseRoles } from 'nest-access-control';
import { ZodSerializerDto } from 'nestjs-zod';
import { QuizService } from './quiz.service';
import { QuizRoutes } from '@app/shared/constants/quiz.constant';
import * as quizDto from '../dto/quiz.dto';
import { User } from '@/guards/user.decorator';
import { Quiz, type User as IUser } from '@/db/schema';
import { QuizDto } from '../dto/quiz.dto';
import { assignDateField } from '@/util/date-util';
import { CustomParseUUIDPipe } from '@/common/pipes/custom-parse-uuid';

@ApiTags('Quiz')
@Controller({ version: '1', path: 'quiz' })
export class QuizController {
  constructor(private readonly quizService: QuizService) {}
  private readonly logger = new Logger(QuizController.name);

  @Post()
  @UseGuards(RoleGuard)
  @UseRoles({ resource: 'quiz', action: 'create', possession: 'any' })
  @ZodSerializerDto(quizDto.QuizDto)
  // @ApiBearerAuth()
  @CLIENT_TYPE(AppClients.WEB)
  @ApiOperation({
    summary: 'Create a new quiz',
    description: 'Create a new quiz with questions from a question bank',
  })
  @ApiBody({
    type: quizDto.QuizDto,
    description: 'Quiz creation parameters',
  })
  @ApiCreatedResponse({
    description: 'Quiz has been successfully created',
    type: quizDto.QuizDto,
  })
  @ApiBadRequestResponse({
    description:
      'Invalid quiz data, such as past start time or insufficient questions',
  })
  async createQuiz(
    @Body() quizInput: quizDto.QuizParams,
    @User() user: IUser,
  ): Promise<any> {
    try {
      const { start_time, end_at } = quizInput;
      const processedStartTime = assignDateField(start_time);
      const processedEndAt = assignDateField(end_at);
      const now = new Date().toISOString();

      if (processedStartTime > processedEndAt) {
        throw new BadRequestException(
          'Start time cannot be greater than end time.',
        );
      }
      if (processedStartTime < now) {
        throw new BadRequestException('Start time cannot be in the past.');
      }

      const questionCount = await this.quizService.questionCountByBankId(
        quizInput.question_bank_id,
      );

      this.logger.log(
        `Quiz validation - Question Bank ID: ${quizInput.question_bank_id}, Available questions (non-golden): ${questionCount}, Required questions: ${quizInput.total_questions}`,
      );

      if (questionCount < quizInput.total_questions) {
        this.logger.error(
          `Insufficient questions - Available: ${questionCount}, Required: ${quizInput.total_questions}`,
        );
        throw new BadRequestException({
          message:
            'Number of questions in the question bank is less than the number of questions required for the quiz',
          availableQuestions: questionCount,
          requiredQuestions: quizInput.total_questions,
        });
      }
      return await this.quizService.createQuiz({
        ...quizInput,
        created_by: user.id,
        start_time: processedStartTime,
        end_at: processedEndAt,
      });
    } catch (error: any) {
      this.logger.error('Failed to create quiz', error.stack);
      throw error;
    }
  }

  @Get()
  @ApiOperation({
    summary: 'Get all quizzes',
    description:
      'Retrieve a list of all quizzes with optional filtering and pagination',
  })
  @ApiQuery({
    name: 'search',
    required: false,
    description: 'Filter quizzes by title',
    type: String,
  })
  @ApiQuery({
    name: 'status',
    required: false,
    description: 'Filter quizzes by status',
    type: String,
  })
  @ApiQuery({
    name: 'page',
    required: false,
    description: 'Page number for pagination',
    type: Number,
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    description: 'Number of items per page',
    type: Number,
  })
  @ApiOkResponse({
    description: 'Quizzes retrieved successfully',
    status: 200,
  })
  @UseGuards(RoleGuard)
  @UseRoles({ resource: 'quiz', action: 'read', possession: 'any' })
  @ApiBearerAuth()
  @CLIENT_TYPE(AppClients.WEB)
  async getQuiz(
    @Query()
    query: quizDto.quizQueryParamsDto,
  ): Promise<any> {
    try {
      return await this.quizService.getAllQuiz(
        query as quizDto.quizQueryParamsDto & {
          sort: keyof Quiz;
        },
      );
    } catch (error: any) {
      this.logger.error('Failed to get quiz', error.stack);
      throw error;
    }
  }
  @Get(QuizRoutes.ACTIVE_QUIZ)
  @ApiOperation({
    summary: 'Get active quizzes for a student',
    description:
      'Retrieve a list of active quizzes available for the current student',
  })
  @ApiQuery({
    name: 'search',
    required: false,
    description: 'Filter quizzes by title',
    type: String,
  })
  @ApiQuery({
    name: 'status',
    required: false,
    description: 'Filter quizzes by status',
    type: String,
  })
  @ApiQuery({
    name: 'page',
    required: false,
    description: 'Page number for pagination',
    type: Number,
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    description: 'Number of items per page',
    type: Number,
  })
  @ApiOkResponse({
    description: 'Active quiz retrieved successfully',
    status: 200,
  })
  @UseGuards(RoleGuard)
  @UseRoles({ resource: 'quiz', action: 'read', possession: 'any' })
  @ApiBearerAuth()
  @CLIENT_TYPE(AppClients.MOBILE)
  async getQuizActive(
    @Query()
    query: quizDto.quizQueryParamsDto,
    @User(['student', 'student_admin']) user: IUser,
  ): Promise<any> {
    try {
      const { id } = user;

      return await this.quizService.getAllQuiz(
        query as quizDto.quizQueryParamsDto & {
          sort: keyof Quiz;
        },
        id,
      );
    } catch (error: any) {
      this.logger.error('Failed to get quiz', error.stack);
      throw error;
    }
  }
  @Get(QuizRoutes.GET_QUIZ)
  @ApiOperation({
    summary: 'Get quiz by ID',
    description: 'Retrieve a specific quiz by its unique identifier',
  })
  @ApiParam({
    name: 'id',
    description: 'Quiz unique identifier',
    type: 'string',
    format: 'uuid',
    required: true,
  })
  @ApiOkResponse({
    description: 'Quiz retrieved successfully',
    status: 200,
  })
  @ApiNotFoundResponse({
    description: 'Quiz with the specified ID not found',
  })
  @UseGuards(RoleGuard)
  @UseRoles({ resource: 'quiz', action: 'read', possession: 'any' })
  @ZodSerializerDto(quizDto.QuizDto)
  @CLIENT_TYPE(AppClients.WEB)
  @ApiBearerAuth()
  async getQuizById(
    @Param('id', new CustomParseUUIDPipe()) id: string,
  ): Promise<any> {
    try {
      return await this.quizService.getQuizById(id);
    } catch (error: any) {
      this.logger.error(error.stack);
      throw error;
    }
  }

  @Put(QuizRoutes.CREATE_SCORE)
  @ApiOperation({
    summary: 'Record student quiz score',
    description: "Update a student's score for a specific quiz",
  })
  @ApiBody({
    type: quizDto.QuizScoreDto,
    description: 'Quiz score data',
  })
  @ApiOkResponse({
    description: 'Quiz score recorded successfully',
  })
  @ApiNotFoundResponse({
    description: 'Quiz or student not found',
  })
  @UseGuards(RoleGuard)
  @UseRoles({ resource: 'quiz-score', action: 'update', possession: 'own' })
  @ApiBearerAuth()
  @CLIENT_TYPE(AppClients.MOBILE)
  async recordStudentQuizScore(
    @Body() data: quizDto.QuizScore,
    @User(['student', 'student_admin']) user: IUser,
  ): Promise<any> {
    try {
      this.logger.log('Recording student quiz score', user);
      return await this.quizService.updateQuizScore(data);
    } catch (error) {
      throw error;
    }
  }
  @Put(QuizRoutes.UPDATE_QUIZ)
  @ApiOperation({
    summary: 'Update quiz',
    description: 'Update an existing quiz by ID',
  })
  @ApiParam({
    name: 'id',
    description: 'Quiz unique identifier',
    type: 'string',
    format: 'uuid',
    required: true,
  })
  @ApiBody({
    type: quizDto.UpdateQuizDto,
    description: 'Updated quiz data',
  })
  @ApiOkResponse({
    description: 'Quiz updated successfully',
    type: quizDto.QuizDto,
  })
  @ApiNotFoundResponse({
    description: 'Quiz with the specified ID not found',
  })
  @ApiBadRequestResponse({
    description: 'Invalid quiz data',
  })
  @UseGuards(RoleGuard)
  @UseRoles({ resource: 'quiz', action: 'update', possession: 'any' })
  @ZodSerializerDto(quizDto.QuizDto)
  @ApiBearerAuth()
  @CLIENT_TYPE(AppClients.WEB)
  async updateQuiz(
    @Body() quizInput: quizDto.UpdateQuizDto,
    @Param('id', new CustomParseUUIDPipe()) id: string,
  ): Promise<QuizDto> {
    try {
      return await this.quizService.updateQuiz(quizInput, id);
    } catch (error) {
      throw error;
    }
  }

  @Delete(QuizRoutes.DELETE_QUIZ)
  @ApiOperation({
    summary: 'Delete quiz',
    description: 'Delete a quiz by ID',
  })
  @ApiParam({
    name: 'id',
    description: 'Quiz unique identifier',
    type: 'string',
    format: 'uuid',
    required: true,
  })
  @ApiOkResponse({
    description: 'Quiz deleted successfully',
  })
  @ApiNotFoundResponse({
    description: 'Quiz with the specified ID not found',
  })
  @ApiUnauthorizedResponse({
    description: 'User is not authorized to delete this quiz',
  })
  @UseGuards(RoleGuard)
  @UseRoles({ resource: 'quiz', action: 'delete', possession: 'any' })
  @ApiBearerAuth()
  @CLIENT_TYPE(AppClients.WEB)
  async deleteQuiz(
    @Param('id', new CustomParseUUIDPipe()) id: string,
    @User(['admin']) user: IUser,
  ): Promise<any> {
    try {
      this.logger.log('Deleting quiz', user.email);
      return await this.quizService.deleteQuiz(id);
    } catch (error: any) {
      this.logger.error('Failed to delete quiz', error.stack);
      throw error;
    }
  }

  @Get(QuizRoutes.GET_QUIZ_QUESTIONS)
  @ApiOperation({
    summary: 'Get quiz questions',
    description: 'Retrieve questions for a specific quiz',
  })
  @ApiParam({
    name: 'id',
    description: 'Quiz unique identifier',
    type: 'string',
    format: 'uuid',
    required: true,
  })
  @ApiOkResponse({
    description: 'Quiz questions retrieved successfully',
    status: 200,
  })
  @ApiNotFoundResponse({
    description: 'Quiz with the specified ID not found',
  })
  @UseGuards(RoleGuard)
  @UseRoles({ resource: 'quiz-question', action: 'read', possession: 'any' })
  @ApiBearerAuth()
  @CLIENT_TYPE(AppClients.MOBILE)
  async getQuizQuestions(
    @Param('id', new CustomParseUUIDPipe()) id: string,
    @User() user: IUser,
  ): Promise<any> {
    try {
      return await this.quizService.getQuizQuestions(id, user.id);
    } catch (error: any) {
      this.logger.error('Failed to get quiz questions', error.stack);
      throw error;
    }
  }

  @Post(QuizRoutes.CREATE_SCORE)
  @ApiOperation({
    summary: 'Create quiz score',
    description: 'Create a new score record for a student taking a quiz',
  })
  @ApiBody({
    type: quizDto.QuizScoreDto,
    description: 'Quiz score data',
  })
  @ApiCreatedResponse({
    description: 'Quiz score created successfully',
  })
  @ApiBadRequestResponse({
    description: 'Invalid quiz score data',
  })
  @ApiNotFoundResponse({
    description: 'Quiz or student not found',
  })
  @UseGuards(RoleGuard)
  @UseRoles({ resource: 'quiz-score', action: 'create', possession: 'own' })
  @ApiBearerAuth()
  @CLIENT_TYPE(AppClients.MOBILE)
  async createQuizScore(
    @Body() quizInput: quizDto.QuizScoreDto,
    @User(['student', 'student_admin']) user: IUser,
  ): Promise<any> {
    try {
      this.logger.log('Creating quiz score', user.email);
      return await this.quizService.createQuizScore(quizInput);
    } catch (error: any) {
      this.logger.error(error.stack);
      throw error;
    }
  }
  @Get(QuizRoutes.GET_SCORE)
  @ApiOperation({
    summary: 'Get quiz score',
    description: "Retrieve a student's score for a specific quiz",
  })
  @ApiParam({
    name: 'quizId',
    description: 'Quiz unique identifier',
    type: 'string',
    format: 'uuid',
    required: true,
  })
  @ApiParam({
    name: 'student_id',
    description: 'Student unique identifier',
    type: 'string',
    format: 'uuid',
    required: true,
  })
  @ApiOkResponse({
    description: 'Quiz score retrieved successfully',
    status: 200,
  })
  @ApiNotFoundResponse({
    description: 'Quiz score not found for the specified quiz and student',
  })
  @UseGuards(RoleGuard)
  @UseRoles({ resource: 'quiz-score', action: 'read', possession: 'any' })
  @ApiBearerAuth()
  @CLIENT_TYPE(AppClients.WEB, AppClients.MOBILE)
  async getQuizScore(
    @Param('quizId', new CustomParseUUIDPipe()) quizId: string,
    @Param('student_id', new CustomParseUUIDPipe()) userId: string,
  ): Promise<any> {
    try {
      return await this.quizService.getQuizScore(quizId, userId);
    } catch (error: any) {
      this.logger.error('Failed to get quiz score', error.stack);
      throw error;
    }
  }

  @Get(QuizRoutes.GET_ALL_QUIZ_SCORES)
  @ApiOperation({
    summary: 'Get all quiz scores',
    description: 'Retrieve all quiz scores across all students and quizzes',
  })
  @ApiOkResponse({
    description: 'Quiz scores retrieved successfully',
    status: 200,
  })
  @UseGuards(RoleGuard)
  @UseRoles({ resource: 'quiz-score', action: 'read', possession: 'any' })
  @ApiBearerAuth()
  @CLIENT_TYPE(AppClients.WEB)
  async getQuizScores(): Promise<quizDto.QuizScore[]> {
    try {
      return await this.quizService.getAllQuizScores();
    } catch (error: any) {
      this.logger.error('Failed to get quiz scores', error.stack);
      throw error;
    }
  }

  @Get(QuizRoutes.GET_QUIZ_BY_QUESTION_BANK)
  @ApiOperation({
    summary: 'Get quizzes by question bank',
    description:
      'Retrieve all quizzes associated with a specific question bank',
  })
  @ApiParam({
    name: 'questionBankId',
    description: 'Question bank unique identifier',
    type: 'string',
    format: 'uuid',
    required: true,
  })
  @ApiQuery({
    name: 'search',
    required: false,
    description: 'Filter quizzes by title',
    type: String,
  })
  @ApiQuery({
    name: 'status',
    required: false,
    description: 'Filter quizzes by status',
    type: String,
  })
  @ApiQuery({
    name: 'page',
    required: false,
    description: 'Page number for pagination',
    type: Number,
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    description: 'Number of items per page',
    type: Number,
  })
  @ApiOkResponse({
    description: 'Quizzes retrieved successfully',
    type: [quizDto.QuizDto],
  })
  @ApiNotFoundResponse({
    description: 'Question bank not found',
  })
  @UseGuards(RoleGuard)
  @UseRoles({ resource: 'quiz-score', action: 'read', possession: 'any' })
  @ApiBearerAuth()
  @CLIENT_TYPE(AppClients.WEB)
  async getQuizByBankId(
    @Param('questionBankId', new CustomParseUUIDPipe()) questionBankId: string,
    @Query() query: quizDto.quizQueryParamsDto,
  ) {
    try {
      return await this.quizService.getQuizByBankId(
        questionBankId,
        query as quizDto.quizQueryParamsDto & { sort: keyof Quiz },
      );
    } catch (error) {
      throw error;
    }
  }
}
